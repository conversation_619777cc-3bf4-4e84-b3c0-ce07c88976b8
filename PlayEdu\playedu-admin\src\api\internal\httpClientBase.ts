import axios, { AxiosInstance, AxiosResponse, AxiosError } from "axios";
import { message } from "antd";

/**
 * HTTP客户端基类
 * 提供通用的请求拦截器和响应处理
 */
export class HttpClientBase {
  protected axios: AxiosInstance;

  constructor(baseURL: string) {
    this.axios = axios.create({
      baseURL,
      timeout: 30000,
      withCredentials: true,
    });

    this.setupInterceptors();
  }

  /**
   * 设置请求和响应拦截器
   */
  private setupInterceptors() {
    // 请求拦截器
    this.axios.interceptors.request.use(
      (config) => {
        const token = this.getToken();
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.axios.interceptors.response.use(
      (response: AxiosResponse) => {
        const { code, msg } = response.data;

        if (code === 0) {
          return Promise.resolve(response);
        } else {
          this.handleBusinessError(code, msg);
          return Promise.reject(response);
        }
      },
      (error: AxiosError) => {
        this.handleHttpError(error);
        return Promise.reject(error.response);
      }
    );
  }

  /**
   * 处理业务错误
   */
  private handleBusinessError(code: number, msg: string) {
    switch (code) {
      case 404:
        message.error(msg);
        this.goError(404);
        break;
      case 403:
        message.error(msg);
        this.goError(403);
        break;
      case 429:
        message.error(msg);
        this.goError(429);
        break;
      case 500:
        message.error(msg);
        this.goError(500);
        break;
      default:
        message.error(msg);
        this.goError(code);
        break;
    }
  }

  /**
   * 处理HTTP错误
   */
  private handleHttpError(error: AxiosError) {
    const status = error.response?.status;
    
    switch (status) {
      case 401:
        message.error("请重新登录");
        this.goLogin();
        break;
      case 404:
        this.goError(404);
        break;
      case 403:
        this.goError(403);
        break;
      case 429:
        this.goError(429);
        break;
      case 500:
        this.goError(500);
        break;
      default:
        if (status) {
          this.goError(status);
        } else {
          message.error("网络错误，请检查网络连接");
        }
        break;
    }
  }

  /**
   * 获取token - 子类需要实现
   */
  protected getToken(): string | null {
    throw new Error("getToken method must be implemented by subclass");
  }

  /**
   * 跳转到登录页 - 子类需要实现
   */
  protected goLogin(): void {
    throw new Error("goLogin method must be implemented by subclass");
  }

  /**
   * 跳转到错误页 - 子类需要实现
   */
  protected goError(code: number): void {
    throw new Error("goError method must be implemented by subclass");
  }

  /**
   * 通用请求方法
   */
  protected async request<T = any>(config: any): Promise<T> {
    const response = await this.axios.request(config);
    return response.data;
  }

  /**
   * GET请求
   */
  async get<T = any>(url: string, params?: object): Promise<T> {
    const response = await this.axios.get(url, { params });
    return response.data;
  }

  /**
   * POST请求
   */
  async post<T = any>(url: string, data?: object): Promise<T> {
    const response = await this.axios.post(url, data);
    return response.data;
  }

  /**
   * PUT请求
   */
  async put<T = any>(url: string, data?: object): Promise<T> {
    const response = await this.axios.put(url, data);
    return response.data;
  }

  /**
   * DELETE请求
   */
  async delete<T = any>(url: string): Promise<T> {
    const response = await this.axios.delete(url);
    return response.data;
  }
}
