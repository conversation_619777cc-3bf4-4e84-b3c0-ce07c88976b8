# SCORM 相关配置
scorm:
  # 存储配置
  storage:
    # SCORM包存储基础路径
    base-path: ${SCORM_STORAGE_PATH:/data/scorm}
    # 解压后的包存储路径
    extract-path: ${SCORM_EXTRACT_PATH:/data/scorm/extracted}
    # 最大包大小 (MB)
    max-package-size: ${SCORM_MAX_SIZE:500}
    # 临时文件清理时间 (小时)
    temp-cleanup-hours: ${SCORM_TEMP_CLEANUP:24}
  
  # 解析配置
  parser:
    # 支持的SCORM版本
    supported-versions:
      - "1.2"
      - "2004"
    # 是否验证manifest.xml
    validate-manifest: ${SCORM_VALIDATE_MANIFEST:true}
    # 解压超时时间 (秒)
    extract-timeout: ${SCORM_EXTRACT_TIMEOUT:300}
    # 解析超时时间 (秒)
    parse-timeout: ${SCORM_PARSE_TIMEOUT:600}
    # 是否异步解析
    async-parse: ${SCORM_ASYNC_PARSE:true}
  
  # 播放器配置
  player:
    # 自动保存间隔 (秒)
    auto-save-interval: ${SCORM_AUTO_SAVE_INTERVAL:30}
    # 最大尝试次数
    max-attempts: ${SCORM_MAX_ATTEMPTS:3}
    # iframe沙箱模式
    sandbox-mode: "allow-scripts allow-same-origin allow-forms allow-popups allow-modals"
    # 是否启用调试模式
    debug-mode: ${SCORM_DEBUG_MODE:false}
  
  # API配置
  api:
    # API基础路径
    base-path: "/api/v1/scorm"
    # 内容服务路径
    content-path: "/content"
    # 是否启用CORS
    enable-cors: ${SCORM_ENABLE_CORS:true}
    # 允许的域名
    allowed-origins: ${SCORM_ALLOWED_ORIGINS:*}
  
  # 安全配置
  security:
    # 是否启用文件类型检查
    file-type-check: ${SCORM_FILE_TYPE_CHECK:true}
    # 允许的文件扩展名
    allowed-extensions:
      - html
      - htm
      - css
      - js
      - json
      - xml
      - png
      - jpg
      - jpeg
      - gif
      - svg
      - ico
      - pdf
      - swf
      - mp4
      - mp3
      - wav
    # 禁止的文件扩展名
    forbidden-extensions:
      - exe
      - bat
      - cmd
      - sh
      - php
      - jsp
      - asp
    # 最大文件大小 (MB)
    max-file-size: ${SCORM_MAX_FILE_SIZE:50}
  
  # 缓存配置
  cache:
    # 是否启用缓存
    enabled: ${SCORM_CACHE_ENABLED:true}
    # 缓存过期时间 (秒)
    expire-time: ${SCORM_CACHE_EXPIRE:3600}
    # 缓存大小限制 (MB)
    max-size: ${SCORM_CACHE_MAX_SIZE:100}
  
  # 日志配置
  logging:
    # 是否启用详细日志
    verbose: ${SCORM_VERBOSE_LOG:false}
    # 日志级别
    level: ${SCORM_LOG_LEVEL:INFO}
    # 是否记录API调用
    log-api-calls: ${SCORM_LOG_API_CALLS:true}

# 线程池配置
spring:
  task:
    execution:
      pool:
        # SCORM解析线程池
        scorm-parse:
          core-size: ${SCORM_THREAD_CORE:2}
          max-size: ${SCORM_THREAD_MAX:5}
          queue-capacity: ${SCORM_THREAD_QUEUE:100}
          thread-name-prefix: "scorm-parse-"
          keep-alive: 60s
