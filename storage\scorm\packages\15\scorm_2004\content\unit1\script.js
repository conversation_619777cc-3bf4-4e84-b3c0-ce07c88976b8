/**
 * SCORM 2004 第一单元 JavaScript逻辑
 */

// 全局变量
let currentSection = 'overview';
let sectionProgress = {
    overview: false,
    objectives: false,
    content: false,
    practice: false,
    assessment: false
};
let contentTabProgress = {
    intro: false,
    features: false,
    architecture: false,
    benefits: false
};
let practiceResults = {
    matching: null,
    sorting: null
};
let assessmentData = {
    questions: [],
    answers: {},
    score: 0,
    attempts: 0
};
let startTime = new Date();
let sessionTimer;

// 测试题目数据
const quizQuestions = [
    {
        id: 'q1',
        type: 'choice',
        question: 'SCORM 2004相比SCORM 1.2的主要改进是什么？',
        options: [
            'A. 更好的图形界面',
            'B. 高级排序和导航功能',
            'C. 更快的加载速度',
            'D. 更小的文件大小'
        ],
        correct: 1,
        weight: 20
    },
    {
        id: 'q2',
        type: 'choice',
        question: 'SCORM的核心理念不包括以下哪项？',
        options: [
            'A. 可重用性',
            'B. 可访问性',
            'C. 互操作性',
            'D. 独占性'
        ],
        correct: 3,
        weight: 20
    },
    {
        id: 'q3',
        type: 'choice',
        question: 'SCORM 2004的技术架构包含几个主要层次？',
        options: [
            'A. 2个',
            'B. 3个',
            'C. 4个',
            'D. 5个'
        ],
        correct: 1,
        weight: 20
    },
    {
        id: 'q4',
        type: 'choice',
        question: '在SCORM 2004中，SCO是指什么？',
        options: [
            'A. 系统配置对象',
            'B. 可共享内容对象',
            'C. 安全控制选项',
            'D. 服务器连接对象'
        ],
        correct: 1,
        weight: 20
    },
    {
        id: 'q5',
        type: 'choice',
        question: 'SCORM 2004支持的最大尝试次数限制是多少？',
        options: [
            'A. 没有限制',
            'B. 最多3次',
            'C. 可以自定义设置',
            'D. 固定为5次'
        ],
        correct: 2,
        weight: 20
    }
];

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    startSessionTimer();
    generateQuiz();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 等待SCORM初始化
    setTimeout(() => {
        if (window.SCORM2004) {
            // 获取学习者信息
            const learnerName = SCORM2004.getValue('cmi.learner_name') || '未知学习者';
            document.getElementById('learnerName').textContent = learnerName;
            
            // 设置初始状态
            SCORM2004.setCompletionStatus(SCORM2004.COMPLETION_STATUS.INCOMPLETE);
            SCORM2004.setSuccessStatus(SCORM2004.SUCCESS_STATUS.UNKNOWN);
            updateScormStatus();
            
            // 恢复学习进度
            const suspendData = SCORM2004.getSuspendData();
            if (suspendData) {
                try {
                    const data = JSON.parse(suspendData);
                    if (data.unit1) {
                        restoreProgress(data.unit1);
                    }
                } catch (e) {
                    console.log('无法解析暂停数据');
                }
            }
            
            // 设置学习者偏好
            const preferences = SCORM2004.getLearnerPreference();
            if (preferences.language) {
                document.documentElement.lang = preferences.language;
            }
        }
    }, 500);
    
    // 标记概览部分为已访问
    sectionProgress.overview = true;
    updateNavigationStatus();
    updateProgress();
}

/**
 * 设置事件监听器
 */
function setupEventListeners() {
    // 导航点击事件
    document.querySelectorAll('.nav-item').forEach(item => {
        item.addEventListener('click', function() {
            const section = this.dataset.section;
            showSection(section);
        });
    });
    
    // 内容标签页切换
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.dataset.tab;
            showTab(tab);
            contentTabProgress[tab] = true;
            checkContentCompletion();
        });
    });
    
    // 拖拽匹配游戏
    setupMatchingGame();
    
    // 排序游戏
    setupSortingGame();
}

/**
 * 开始会话计时器
 */
function startSessionTimer() {
    sessionTimer = setInterval(() => {
        const now = new Date();
        const elapsed = Math.floor((now - startTime) / 1000);
        const hours = Math.floor(elapsed / 3600);
        const minutes = Math.floor((elapsed % 3600) / 60);
        const seconds = elapsed % 60;
        
        const timeString = String(hours).padStart(2, '0') + ':' + 
                          String(minutes).padStart(2, '0') + ':' + 
                          String(seconds).padStart(2, '0');
        
        document.getElementById('sessionTime').textContent = timeString;
        
        // 更新进度测量
        const progressMeasure = calculateProgressMeasure();
        if (window.SCORM2004) {
            SCORM2004.setProgressMeasure(progressMeasure);
        }
    }, 1000);
}

/**
 * 显示指定章节
 */
function showSection(sectionName) {
    // 隐藏所有章节
    document.querySelectorAll('.content-section').forEach(section => {
        section.classList.remove('active');
    });
    
    // 显示指定章节
    const targetSection = document.getElementById(sectionName);
    if (targetSection) {
        targetSection.classList.add('active');
        currentSection = sectionName;
        
        // 更新导航状态
        document.querySelectorAll('.nav-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-section="${sectionName}"]`).classList.add('active');
        
        // 标记为已访问
        sectionProgress[sectionName] = true;
        updateNavigationStatus();
        updateProgress();
        saveProgress();
    }
}

/**
 * 显示内容标签页
 */
function showTab(tabName) {
    // 隐藏所有标签页
    document.querySelectorAll('.tab-panel').forEach(panel => {
        panel.classList.remove('active');
    });
    document.querySelectorAll('.tab-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 显示指定标签页
    document.getElementById(tabName).classList.add('active');
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
}

/**
 * 下一章节
 */
function nextSection() {
    const sections = ['overview', 'objectives', 'content', 'practice', 'assessment'];
    const currentIndex = sections.indexOf(currentSection);
    if (currentIndex < sections.length - 1) {
        showSection(sections[currentIndex + 1]);
    }
}

/**
 * 上一章节
 */
function prevSection() {
    const sections = ['overview', 'objectives', 'content', 'practice', 'assessment'];
    const currentIndex = sections.indexOf(currentSection);
    if (currentIndex > 0) {
        showSection(sections[currentIndex - 1]);
    }
}

/**
 * 更新导航状态
 */
function updateNavigationStatus() {
    Object.keys(sectionProgress).forEach(section => {
        const statusElement = document.getElementById(section + 'Status');
        if (statusElement) {
            statusElement.textContent = sectionProgress[section] ? '●' : '○';
            statusElement.style.color = sectionProgress[section] ? '#27ae60' : '#bdc3c7';
        }
    });
}

/**
 * 更新进度条
 */
function updateProgress() {
    const completedSections = Object.values(sectionProgress).filter(Boolean).length;
    const totalSections = Object.keys(sectionProgress).length;
    const progress = (completedSections / totalSections) * 100;
    
    document.getElementById('progressFill').style.width = progress + '%';
    document.getElementById('progressText').textContent = Math.round(progress) + '%';
}

/**
 * 计算进度测量值
 */
function calculateProgressMeasure() {
    let totalWeight = 0;
    let completedWeight = 0;
    
    // 章节权重
    const sectionWeights = {
        overview: 0.1,
        objectives: 0.1,
        content: 0.4,
        practice: 0.2,
        assessment: 0.2
    };
    
    Object.keys(sectionProgress).forEach(section => {
        totalWeight += sectionWeights[section];
        if (sectionProgress[section]) {
            completedWeight += sectionWeights[section];
        }
    });
    
    // 内容标签页额外权重
    const completedTabs = Object.values(contentTabProgress).filter(Boolean).length;
    const tabProgress = completedTabs / Object.keys(contentTabProgress).length;
    completedWeight += (sectionWeights.content * 0.5 * tabProgress);
    
    return totalWeight > 0 ? (completedWeight / totalWeight) : 0;
}

/**
 * 检查内容部分完成情况
 */
function checkContentCompletion() {
    const allTabsVisited = Object.values(contentTabProgress).every(Boolean);
    if (allTabsVisited && !sectionProgress.content) {
        sectionProgress.content = true;
        updateNavigationStatus();
        updateProgress();
        saveProgress();
    }
}

/**
 * 设置匹配游戏
 */
function setupMatchingGame() {
    const matchItems = document.querySelectorAll('.match-item');
    const matchTargets = document.querySelectorAll('.match-target');
    
    matchItems.forEach(item => {
        item.addEventListener('dragstart', function(e) {
            e.dataTransfer.setData('text/plain', this.dataset.id);
        });
    });
    
    matchTargets.forEach(target => {
        target.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        
        target.addEventListener('drop', function(e) {
            e.preventDefault();
            const draggedId = e.dataTransfer.getData('text/plain');
            const targetId = this.dataset.target;
            
            if (draggedId === targetId) {
                this.style.backgroundColor = '#d4edda';
                this.style.border = '2px solid #27ae60';
                checkMatchingCompletion();
            } else {
                this.style.backgroundColor = '#f8d7da';
                this.style.border = '2px solid #e74c3c';
                setTimeout(() => {
                    this.style.backgroundColor = '';
                    this.style.border = '';
                }, 1000);
            }
        });
    });
}

/**
 * 检查匹配游戏完成情况
 */
function checkMatchingCompletion() {
    const targets = document.querySelectorAll('.match-target');
    const correctMatches = Array.from(targets).filter(target => 
        target.style.backgroundColor === 'rgb(212, 237, 218)'
    ).length;
    
    if (correctMatches === targets.length) {
        practiceResults.matching = true;
        document.getElementById('matchingResult').style.display = 'block';
        document.querySelector('#matchingResult .result-text').textContent = '恭喜！匹配全部正确！';
        
        // 记录交互到SCORM
        if (window.SCORM2004) {
            SCORM2004.addInteraction(
                'matching_game',
                SCORM2004.INTERACTION_TYPE.MATCHING,
                ['scorm', 'sco', 'lms', 'api'],
                new Date().toISOString(),
                'scorm:电子学习标准规范,sco:可共享内容对象,lms:学习管理系统,api:应用程序接口',
                1.0,
                'all_correct',
                SCORM2004.RESULT.CORRECT
            );
        }
    }
}

/**
 * 设置排序游戏
 */
function setupSortingGame() {
    const sortItems = document.querySelectorAll('.sort-item');
    let draggedElement = null;
    
    sortItems.forEach(item => {
        item.draggable = true;
        
        item.addEventListener('dragstart', function(e) {
            draggedElement = this;
            this.style.opacity = '0.5';
        });
        
        item.addEventListener('dragend', function() {
            this.style.opacity = '';
            draggedElement = null;
        });
        
        item.addEventListener('dragover', function(e) {
            e.preventDefault();
        });
        
        item.addEventListener('drop', function(e) {
            e.preventDefault();
            if (draggedElement && draggedElement !== this) {
                const container = this.parentNode;
                const draggedIndex = Array.from(container.children).indexOf(draggedElement);
                const targetIndex = Array.from(container.children).indexOf(this);
                
                if (draggedIndex < targetIndex) {
                    container.insertBefore(draggedElement, this.nextSibling);
                } else {
                    container.insertBefore(draggedElement, this);
                }
            }
        });
    });
}

/**
 * 检查排序结果
 */
function checkSorting() {
    const sortItems = document.querySelectorAll('.sort-item');
    const currentOrder = Array.from(sortItems).map(item => parseInt(item.dataset.order));
    const correctOrder = [1, 2, 3, 4];
    
    const isCorrect = JSON.stringify(currentOrder) === JSON.stringify(correctOrder);
    
    const resultDiv = document.getElementById('sortingResult');
    const resultText = resultDiv.querySelector('.result-text');
    
    if (isCorrect) {
        practiceResults.sorting = true;
        resultText.textContent = '排序正确！开发流程为：分析需求 → 开发内容 → 打包内容 → 测试部署';
        resultDiv.style.display = 'block';
        resultDiv.style.backgroundColor = '#d4edda';
        
        // 记录交互到SCORM
        if (window.SCORM2004) {
            SCORM2004.addInteraction(
                'sorting_game',
                SCORM2004.INTERACTION_TYPE.SEQUENCING,
                ['development_process'],
                new Date().toISOString(),
                '1,2,3,4',
                1.0,
                currentOrder.join(','),
                SCORM2004.RESULT.CORRECT
            );
        }
    } else {
        resultText.textContent = '排序不正确，请重新尝试。正确顺序应该是开发流程的逻辑顺序。';
        resultDiv.style.display = 'block';
        resultDiv.style.backgroundColor = '#f8d7da';
        
        // 记录交互到SCORM
        if (window.SCORM2004) {
            SCORM2004.addInteraction(
                'sorting_game',
                SCORM2004.INTERACTION_TYPE.SEQUENCING,
                ['development_process'],
                new Date().toISOString(),
                '1,2,3,4',
                1.0,
                currentOrder.join(','),
                SCORM2004.RESULT.INCORRECT
            );
        }
    }
}

/**
 * 生成测试题目
 */
function generateQuiz() {
    const container = document.getElementById('quizContainer');
    container.innerHTML = '';
    
    quizQuestions.forEach((question, index) => {
        const questionDiv = document.createElement('div');
        questionDiv.className = 'quiz-question';
        questionDiv.innerHTML = `
            <h4>问题 ${index + 1}：${question.question}</h4>
            <div class="quiz-options">
                ${question.options.map((option, optIndex) => `
                    <label class="quiz-option">
                        <input type="radio" name="${question.id}" value="${optIndex}">
                        ${option}
                    </label>
                `).join('')}
            </div>
        `;
        container.appendChild(questionDiv);
    });
}

/**
 * 提交评估
 */
function submitAssessment() {
    // 收集答案
    assessmentData.answers = {};
    let answeredCount = 0;
    
    quizQuestions.forEach(question => {
        const selectedOption = document.querySelector(`input[name="${question.id}"]:checked`);
        if (selectedOption) {
            assessmentData.answers[question.id] = parseInt(selectedOption.value);
            answeredCount++;
        }
    });
    
    if (answeredCount < quizQuestions.length) {
        alert('请回答所有问题后再提交！');
        return;
    }
    
    // 计算分数
    let totalScore = 0;
    let correctCount = 0;
    
    quizQuestions.forEach(question => {
        const userAnswer = assessmentData.answers[question.id];
        if (userAnswer === question.correct) {
            totalScore += question.weight;
            correctCount++;
        }
        
        // 记录每个问题的交互
        if (window.SCORM2004) {
            SCORM2004.addInteraction(
                question.id,
                SCORM2004.INTERACTION_TYPE.CHOICE,
                ['unit1_knowledge'],
                new Date().toISOString(),
                question.correct.toString(),
                question.weight / 100,
                userAnswer.toString(),
                userAnswer === question.correct ? SCORM2004.RESULT.CORRECT : SCORM2004.RESULT.INCORRECT
            );
        }
    });
    
    assessmentData.score = totalScore;
    assessmentData.attempts++;
    
    // 显示结果
    showAssessmentResult(totalScore, correctCount);
    
    // 更新SCORM状态
    if (window.SCORM2004) {
        const scaledScore = totalScore / 100;
        SCORM2004.setScore(scaledScore, totalScore, 0, 100);
        
        if (totalScore >= 75) {
            SCORM2004.setSuccessStatus(SCORM2004.SUCCESS_STATUS.PASSED);
        } else {
            SCORM2004.setSuccessStatus(SCORM2004.SUCCESS_STATUS.FAILED);
        }
        
        SCORM2004.commit();
        updateScormStatus();
    }
    
    document.getElementById('submitBtn').style.display = 'none';
    document.getElementById('completeBtn').style.display = 'inline-block';
}

/**
 * 显示评估结果
 */
function showAssessmentResult(score, correctCount) {
    const resultDiv = document.getElementById('assessmentResult');
    const accuracy = Math.round((correctCount / quizQuestions.length) * 100);
    const passed = score >= 75;
    
    document.getElementById('finalScore').textContent = score + '分';
    document.getElementById('accuracy').textContent = accuracy + '%';
    document.getElementById('passStatus').textContent = passed ? '通过' : '未通过';
    document.getElementById('passStatus').style.color = passed ? '#27ae60' : '#e74c3c';
    
    // 生成反馈
    const feedbackDiv = document.getElementById('resultFeedback');
    let feedback = '';
    
    if (passed) {
        feedback = `
            <div class="feedback-success">
                <h4>🎉 恭喜通过测试！</h4>
                <p>您已经很好地掌握了本单元的核心知识点。</p>
                <ul>
                    <li>正确回答了 ${correctCount} 道题目</li>
                    <li>达到了单元学习目标</li>
                    <li>可以继续下一单元的学习</li>
                </ul>
            </div>
        `;
    } else {
        feedback = `
            <div class="feedback-retry">
                <h4>📚 需要继续努力</h4>
                <p>建议您重新学习相关内容后再次尝试。</p>
                <ul>
                    <li>正确回答了 ${correctCount} 道题目</li>
                    <li>需要达到75分才能通过</li>
                    <li>建议重点复习核心概念部分</li>
                </ul>
            </div>
        `;
    }
    
    feedbackDiv.innerHTML = feedback;
    resultDiv.style.display = 'block';
}

/**
 * 完成单元
 */
function completeUnit() {
    if (window.SCORM2004) {
        SCORM2004.setCompletionStatus(SCORM2004.COMPLETION_STATUS.COMPLETED);
        
        // 设置最终进度
        SCORM2004.setProgressMeasure(1.0);
        
        SCORM2004.commit();
        updateScormStatus();
    }
    
    // 标记评估部分完成
    sectionProgress.assessment = true;
    updateNavigationStatus();
    updateProgress();
    saveProgress();
    
    alert('恭喜您完成了第一单元的学习！您可以继续学习下一单元。');
}

/**
 * 更新SCORM状态显示
 */
function updateScormStatus() {
    if (window.SCORM2004) {
        const completionStatus = SCORM2004.getCompletionStatus();
        const successStatus = SCORM2004.getSuccessStatus();
        
        const completionText = {
            'completed': '已完成',
            'incomplete': '进行中',
            'not attempted': '未开始',
            'unknown': '未知'
        };
        
        const successText = {
            'passed': '已通过',
            'failed': '未通过',
            'unknown': '未知'
        };
        
        document.getElementById('completionStatus').textContent = completionText[completionStatus] || completionStatus;
        document.getElementById('successStatus').textContent = successText[successStatus] || successStatus;
    }
}

/**
 * 保存学习进度
 */
function saveProgress() {
    if (window.SCORM2004) {
        const progressData = {
            unit1: {
                currentSection: currentSection,
                sectionProgress: sectionProgress,
                contentTabProgress: contentTabProgress,
                practiceResults: practiceResults,
                assessmentData: assessmentData,
                timestamp: new Date().toISOString()
            }
        };
        
        SCORM2004.setSuspendData(JSON.stringify(progressData));
        SCORM2004.commit();
    }
}

/**
 * 恢复学习进度
 */
function restoreProgress(data) {
    if (data.currentSection) {
        currentSection = data.currentSection;
        showSection(currentSection);
    }
    
    if (data.sectionProgress) {
        sectionProgress = { ...sectionProgress, ...data.sectionProgress };
        updateNavigationStatus();
    }
    
    if (data.contentTabProgress) {
        contentTabProgress = { ...contentTabProgress, ...data.contentTabProgress };
    }
    
    if (data.practiceResults) {
        practiceResults = { ...practiceResults, ...data.practiceResults };
    }
    
    if (data.assessmentData) {
        assessmentData = { ...assessmentData, ...data.assessmentData };
        
        // 恢复测试答案
        Object.keys(assessmentData.answers).forEach(questionId => {
            const answer = assessmentData.answers[questionId];
            const radio = document.querySelector(`input[name="${questionId}"][value="${answer}"]`);
            if (radio) radio.checked = true;
        });
        
        // 如果已经提交过，显示结果
        if (assessmentData.score > 0) {
            const correctCount = quizQuestions.filter(q => 
                assessmentData.answers[q.id] === q.correct
            ).length;
            showAssessmentResult(assessmentData.score, correctCount);
            document.getElementById('submitBtn').style.display = 'none';
            document.getElementById('completeBtn').style.display = 'inline-block';
        }
    }
    
    updateProgress();
}

/**
 * 播放介绍视频（示例）
 */
function playIntroVideo() {
    alert('这里可以播放SCORM介绍视频。在实际课件中，这里会嵌入真实的视频播放器。');
    
    // 标记视频为已观看
    contentTabProgress.intro = true;
    checkContentCompletion();
}

/**
 * 页面卸载前保存数据
 */
window.addEventListener('beforeunload', function() {
    saveProgress();
    if (sessionTimer) {
        clearInterval(sessionTimer);
    }
});

/**
 * 键盘导航支持
 */
document.addEventListener('keydown', function(e) {
    if (e.key === 'ArrowLeft') {
        prevSection();
    } else if (e.key === 'ArrowRight') {
        nextSection();
    }
});
