<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="77d49b78-0541-4089-8bda-8e5e8ebb337c" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
      <option value="repository" />
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/PlayEdu" />
    <option name="RESET_MODE" value="HARD" />
    <option name="UPDATE_TYPE" value="REBASE" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="D:\opensource\maven\apache-maven-3.9.9" />
        <option name="localRepository" value="D:\opensource\maven\apache-maven-3.9.9\repository" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\opensource\maven\apache-maven-3.9.9\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30oSXwxGSl9FhcHSuB0TIPCIyia" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Maven.playedu [clean].executor": "Run",
    "Maven.playedu-api [clean].executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "Spring Boot.PlayeduApiApplication.executor": "Debug",
    "git-widget-placeholder": "main",
    "last_opened_file_path": "D:/code/learn/playedu",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "SDK",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.0",
    "settings.editor.selected.configurable": "MavenSettings",
    "ts.external.directory.path": "D:\\code\\learn\\playedu\\PlayEdu\\playedu-admin\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-api\playedu-course\src\main\java\xyz\playedu\course\mapper" />
      <recent name="D:\code\learn\playedu\PlayEdu\docs" />
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-pc\src\pages\course" />
      <recent name="D:\code\learn\playedu\PlayEdu\playedu-pc\src\pages\course\compenents" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager">
    <configuration name="PlayeduApiApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="playedu-api" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="xyz.playedu.api.PlayeduApiApplication" />
      <option name="VM_PARAMETERS" value="-Dspring.profiles.active=dev" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-fbdcb00ec9e3-intellij.indexing.shared.core-IU-251.25410.129" />
        <option value="bundled-js-predefined-d6986cc7102b-6a121458b545-JavaScript-IU-251.25410.129" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="77d49b78-0541-4089-8bda-8e5e8ebb337c" name="更改" comment="" />
      <created>1754292476172</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754292476172</updated>
      <workItem from="1754292477493" duration="21054000" />
      <workItem from="1754358685876" duration="3320000" />
      <workItem from="1754363092378" duration="613000" />
      <workItem from="1754363730518" duration="13773000" />
      <workItem from="1754378314788" duration="21192000" />
      <workItem from="1754441550679" duration="2719000" />
      <workItem from="1754444893810" duration="13572000" />
      <workItem from="1754464128285" duration="12831000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="main" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint type="java-line">
          <url>file://$PROJECT_DIR$/PlayEdu/playedu-api/playedu-api/src/main/java/xyz/playedu/api/controller/backend/UploadController.java</url>
          <line>130</line>
          <option name="timeStamp" value="3" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>