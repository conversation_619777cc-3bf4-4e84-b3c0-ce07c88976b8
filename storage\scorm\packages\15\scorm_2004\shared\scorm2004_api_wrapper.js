/**
 * SCORM 2004 API Wrapper
 * 提供与LMS通信的SCORM 2004标准接口
 */

var g_nFindAPITries = 0;
var g_objAPI = null;
var g_bInitDone = false;
var g_bTerminateDone = false;
var g_dtmInitialized = new Date();
var g_strStudentID = "";
var g_strStudentName = "";

// SCORM 2004 常量
var SCORM_TRUE = "true";
var SCORM_FALSE = "false";
var SCORM_NO_ERROR = "0";

// 完成状态常量
var COMPLETION_STATUS_COMPLETED = "completed";
var COMPLETION_STATUS_INCOMPLETE = "incomplete";
var COMPLETION_STATUS_NOT_ATTEMPTED = "not attempted";
var COMPLETION_STATUS_UNKNOWN = "unknown";

// 成功状态常量
var SUCCESS_STATUS_PASSED = "passed";
var SUCCESS_STATUS_FAILED = "failed";
var SUCCESS_STATUS_UNKNOWN = "unknown";

// 退出状态常量
var EXIT_NORMAL = "normal";
var EXIT_SUSPEND = "suspend";
var EXIT_LOGOUT = "logout";
var EXIT_TIME_OUT = "time-out";

// 交互类型常量
var INTERACTION_TRUE_FALSE = "true-false";
var INTERACTION_CHOICE = "choice";
var INTERACTION_FILL_IN = "fill-in";
var INTERACTION_LONG_FILL_IN = "long-fill-in";
var INTERACTION_MATCHING = "matching";
var INTERACTION_PERFORMANCE = "performance";
var INTERACTION_SEQUENCING = "sequencing";
var INTERACTION_LIKERT = "likert";
var INTERACTION_NUMERIC = "numeric";
var INTERACTION_OTHER = "other";

// 交互结果常量
var RESULT_CORRECT = "correct";
var RESULT_INCORRECT = "incorrect";
var RESULT_UNANTICIPATED = "unanticipated";
var RESULT_NEUTRAL = "neutral";

/**
 * 查找SCORM 2004 API对象
 */
function findAPI(win) {
    var nFindAPITries = 0;
    var API = null;
    
    while ((win.API_1484_11 == null) && (win.parent != null) && (win.parent != win)) {
        nFindAPITries++;
        if (nFindAPITries > 500) {
            alert("查找SCORM 2004 API时出错 - 尝试次数过多");
            return null;
        }
        win = win.parent;
    }
    return win.API_1484_11;
}

/**
 * 跨域SCORM API代理对象
 */
var crossDomainAPI = {
    callId: 0,
    pendingCalls: {},

    callMethod: function(method, args) {
        return new Promise((resolve, reject) => {
            var id = ++this.callId;
            this.pendingCalls[id] = { resolve, reject };

            window.parent.postMessage({
                type: 'SCORM_API_CALL',
                method: method,
                args: args || [],
                id: id
            }, '*');

            // 设置超时
            setTimeout(() => {
                if (this.pendingCalls[id]) {
                    delete this.pendingCalls[id];
                    reject(new Error('SCORM API调用超时'));
                }
            }, 5000);
        });
    },

    Initialize: function(parameter) {
        return this.callMethod('Initialize', [parameter]);
    },

    Terminate: function(parameter) {
        return this.callMethod('Terminate', [parameter]);
    },

    GetValue: function(element) {
        return this.callMethod('GetValue', [element]);
    },

    SetValue: function(element, value) {
        return this.callMethod('SetValue', [element, value]);
    },

    Commit: function(parameter) {
        return this.callMethod('Commit', [parameter]);
    },

    GetLastError: function() {
        return this.callMethod('GetLastError', []);
    },

    GetErrorString: function(errorCode) {
        return this.callMethod('GetErrorString', [errorCode]);
    },

    GetDiagnostic: function(parameter) {
        return this.callMethod('GetDiagnostic', [parameter]);
    }
};

// 监听来自父窗口的响应
window.addEventListener('message', function(event) {
    if (event.data.type === 'SCORM_API_RESPONSE') {
        var call = crossDomainAPI.pendingCalls[event.data.id];
        if (call) {
            delete crossDomainAPI.pendingCalls[event.data.id];
            if (event.data.error) {
                call.reject(new Error(event.data.error));
            } else {
                call.resolve(event.data.result);
            }
        }
    }
});

/**
 * 获取API对象
 */
function getAPI() {
    // 首先尝试传统方式
    var theAPI = findAPI(window);
    if ((theAPI == null) && (window.opener != null) && (typeof(window.opener) != "undefined")) {
        theAPI = findAPI(window.opener);
    }

    // 如果传统方式失败，使用跨域API代理
    if (theAPI == null) {
        console.log("使用跨域SCORM API代理");
        return crossDomainAPI;
    }

    return theAPI;
}

/**
 * 初始化SCORM 2004通信
 */
function doInitialize() {
    if (g_bInitDone) return SCORM_TRUE;
    
    var api = getAPI();
    if (api == null) {
        alert("无法获取SCORM 2004 API，初始化失败");
        return SCORM_FALSE;
    }
    
    var result = api.Initialize("");
    if (result == SCORM_TRUE) {
        g_bInitDone = true;
        g_objAPI = api;
        
        // 获取学习者信息
        g_strStudentName = doGetValue("cmi.learner_name");
        g_strStudentID = doGetValue("cmi.learner_id");
        
        // 设置初始状态
        var completionStatus = doGetValue("cmi.completion_status");
        if (completionStatus === "" || completionStatus === COMPLETION_STATUS_UNKNOWN) {
            doSetValue("cmi.completion_status", COMPLETION_STATUS_INCOMPLETE);
        }
        
        var successStatus = doGetValue("cmi.success_status");
        if (successStatus === "" || successStatus === SUCCESS_STATUS_UNKNOWN) {
            doSetValue("cmi.success_status", SUCCESS_STATUS_UNKNOWN);
        }
        
        // 设置进入模式
        doSetValue("cmi.entry", "ab-initio");
        
        console.log("SCORM 2004初始化成功");
        console.log("学习者姓名: " + g_strStudentName);
        console.log("学习者ID: " + g_strStudentID);
        
        return SCORM_TRUE;
    } else {
        var errCode = api.GetLastError();
        var errDesc = api.GetErrorString(errCode);
        var errDiagnostic = api.GetDiagnostic(errCode);
        alert("SCORM 2004初始化失败: " + errCode + " - " + errDesc + "\n" + errDiagnostic);
        return SCORM_FALSE;
    }
}

/**
 * 设置数据值
 */
function doSetValue(name, value) {
    if (!g_bInitDone) {
        if (!doInitialize()) return SCORM_FALSE;
    }
    
    var api = g_objAPI;
    if (api == null) {
        alert("无法获取SCORM 2004 API");
        return SCORM_FALSE;
    }
    
    var result = api.SetValue(name, value);
    if (result == SCORM_FALSE) {
        var errCode = api.GetLastError();
        var errDesc = api.GetErrorString(errCode);
        var errDiagnostic = api.GetDiagnostic(errCode);
        console.error("设置值失败: " + name + " = " + value + " (" + errCode + " - " + errDesc + ")\n" + errDiagnostic);
    }
    
    return result;
}

/**
 * 获取数据值
 */
function doGetValue(name) {
    if (!g_bInitDone) {
        if (!doInitialize()) return "";
    }
    
    var api = g_objAPI;
    if (api == null) {
        alert("无法获取SCORM 2004 API");
        return "";
    }
    
    var value = api.GetValue(name);
    var errCode = api.GetLastError();
    
    if (errCode != SCORM_NO_ERROR) {
        var errDesc = api.GetErrorString(errCode);
        var errDiagnostic = api.GetDiagnostic(errCode);
        console.error("获取值失败: " + name + " (" + errCode + " - " + errDesc + ")\n" + errDiagnostic);
        return "";
    }
    
    return value;
}

/**
 * 提交数据到LMS
 */
function doCommit() {
    if (!g_bInitDone) return SCORM_FALSE;
    
    var api = g_objAPI;
    if (api == null) {
        alert("无法获取SCORM 2004 API");
        return SCORM_FALSE;
    }
    
    var result = api.Commit("");
    if (result == SCORM_FALSE) {
        var errCode = api.GetLastError();
        var errDesc = api.GetErrorString(errCode);
        var errDiagnostic = api.GetDiagnostic(errCode);
        console.error("提交数据失败: " + errCode + " - " + errDesc + "\n" + errDiagnostic);
    }
    
    return result;
}

/**
 * 终止学习会话
 */
function doTerminate() {
    if (g_bTerminateDone) return SCORM_TRUE;
    if (!g_bInitDone) return SCORM_FALSE;
    
    var api = g_objAPI;
    if (api == null) {
        alert("无法获取SCORM 2004 API");
        return SCORM_FALSE;
    }
    
    // 计算学习时间
    var dtmEnd = new Date();
    var nTotalMs = dtmEnd.getTime() - g_dtmInitialized.getTime();
    var nTotalSec = nTotalMs / 1000;
    var nHours = Math.floor(nTotalSec / 3600);
    var nMinutes = Math.floor((nTotalSec % 3600) / 60);
    var nSeconds = Math.floor(nTotalSec % 60);
    
    var strTime = "PT" + nHours + "H" + nMinutes + "M" + nSeconds + "S";
    doSetValue("cmi.session_time", strTime);
    
    doCommit();
    
    var result = api.Terminate("");
    if (result == SCORM_TRUE) {
        g_bTerminateDone = true;
        console.log("SCORM 2004会话结束，学习时间: " + strTime);
    } else {
        var errCode = api.GetLastError();
        var errDesc = api.GetErrorString(errCode);
        var errDiagnostic = api.GetDiagnostic(errCode);
        console.error("结束会话失败: " + errCode + " - " + errDesc + "\n" + errDiagnostic);
    }
    
    return result;
}

/**
 * 设置完成状态
 */
function setCompletionStatus(status) {
    return doSetValue("cmi.completion_status", status);
}

/**
 * 获取完成状态
 */
function getCompletionStatus() {
    return doGetValue("cmi.completion_status");
}

/**
 * 设置成功状态
 */
function setSuccessStatus(status) {
    return doSetValue("cmi.success_status", status);
}

/**
 * 获取成功状态
 */
function getSuccessStatus() {
    return doGetValue("cmi.success_status");
}

/**
 * 设置分数
 */
function setScore(scaled, raw, min, max) {
    var result = true;
    if (scaled !== undefined) result &= doSetValue("cmi.score.scaled", scaled);
    if (raw !== undefined) result &= doSetValue("cmi.score.raw", raw);
    if (min !== undefined) result &= doSetValue("cmi.score.min", min);
    if (max !== undefined) result &= doSetValue("cmi.score.max", max);
    return result;
}

/**
 * 获取分数
 */
function getScore() {
    return {
        scaled: doGetValue("cmi.score.scaled"),
        raw: doGetValue("cmi.score.raw"),
        min: doGetValue("cmi.score.min"),
        max: doGetValue("cmi.score.max")
    };
}

/**
 * 设置退出状态
 */
function setExit(status) {
    return doSetValue("cmi.exit", status);
}

/**
 * 设置进度测量
 */
function setProgressMeasure(measure) {
    return doSetValue("cmi.progress_measure", measure);
}

/**
 * 获取进度测量
 */
function getProgressMeasure() {
    return doGetValue("cmi.progress_measure");
}

/**
 * 添加交互记录
 */
function addInteraction(id, type, objectives, timestamp, correctResponses, weighting, learnerResponse, result, latency, description) {
    var interactionIndex = doGetValue("cmi.interactions._count");
    var index = interactionIndex === "" ? 0 : parseInt(interactionIndex);
    
    var prefix = "cmi.interactions." + index + ".";
    
    var success = true;
    success &= doSetValue(prefix + "id", id);
    success &= doSetValue(prefix + "type", type);
    
    if (objectives && objectives.length > 0) {
        for (var i = 0; i < objectives.length; i++) {
            success &= doSetValue(prefix + "objectives." + i + ".id", objectives[i]);
        }
    }
    
    if (timestamp) success &= doSetValue(prefix + "timestamp", timestamp);
    if (correctResponses) success &= doSetValue(prefix + "correct_responses.0.pattern", correctResponses);
    if (weighting !== undefined) success &= doSetValue(prefix + "weighting", weighting);
    if (learnerResponse) success &= doSetValue(prefix + "learner_response", learnerResponse);
    if (result) success &= doSetValue(prefix + "result", result);
    if (latency) success &= doSetValue(prefix + "latency", latency);
    if (description) success &= doSetValue(prefix + "description", description);
    
    return success;
}

/**
 * 设置暂停数据
 */
function setSuspendData(data) {
    return doSetValue("cmi.suspend_data", data);
}

/**
 * 获取暂停数据
 */
function getSuspendData() {
    return doGetValue("cmi.suspend_data");
}

/**
 * 设置学习者偏好
 */
function setLearnerPreference(audioLevel, language, deliverySpeed, audioCaptioning) {
    var result = true;
    if (audioLevel !== undefined) result &= doSetValue("cmi.learner_preference.audio_level", audioLevel);
    if (language) result &= doSetValue("cmi.learner_preference.language", language);
    if (deliverySpeed !== undefined) result &= doSetValue("cmi.learner_preference.delivery_speed", deliverySpeed);
    if (audioCaptioning !== undefined) result &= doSetValue("cmi.learner_preference.audio_captioning", audioCaptioning);
    return result;
}

/**
 * 获取学习者偏好
 */
function getLearnerPreference() {
    return {
        audioLevel: doGetValue("cmi.learner_preference.audio_level"),
        language: doGetValue("cmi.learner_preference.language"),
        deliverySpeed: doGetValue("cmi.learner_preference.delivery_speed"),
        audioCaptioning: doGetValue("cmi.learner_preference.audio_captioning")
    };
}

/**
 * 页面卸载时自动终止
 */
window.addEventListener('beforeunload', function(e) {
    if (g_bInitDone && !g_bTerminateDone) {
        doTerminate();
    }
});

/**
 * 页面加载完成后自动初始化
 */
window.addEventListener('load', function() {
    doInitialize();
});

// 导出主要函数供外部使用
window.SCORM2004 = {
    initialize: doInitialize,
    setValue: doSetValue,
    getValue: doGetValue,
    commit: doCommit,
    terminate: doTerminate,
    setCompletionStatus: setCompletionStatus,
    getCompletionStatus: getCompletionStatus,
    setSuccessStatus: setSuccessStatus,
    getSuccessStatus: getSuccessStatus,
    setScore: setScore,
    getScore: getScore,
    setExit: setExit,
    setProgressMeasure: setProgressMeasure,
    getProgressMeasure: getProgressMeasure,
    addInteraction: addInteraction,
    setSuspendData: setSuspendData,
    getSuspendData: getSuspendData,
    setLearnerPreference: setLearnerPreference,
    getLearnerPreference: getLearnerPreference,
    
    // 常量
    COMPLETION_STATUS: {
        COMPLETED: COMPLETION_STATUS_COMPLETED,
        INCOMPLETE: COMPLETION_STATUS_INCOMPLETE,
        NOT_ATTEMPTED: COMPLETION_STATUS_NOT_ATTEMPTED,
        UNKNOWN: COMPLETION_STATUS_UNKNOWN
    },
    SUCCESS_STATUS: {
        PASSED: SUCCESS_STATUS_PASSED,
        FAILED: SUCCESS_STATUS_FAILED,
        UNKNOWN: SUCCESS_STATUS_UNKNOWN
    },
    EXIT: {
        NORMAL: EXIT_NORMAL,
        SUSPEND: EXIT_SUSPEND,
        LOGOUT: EXIT_LOGOUT,
        TIME_OUT: EXIT_TIME_OUT
    },
    INTERACTION_TYPE: {
        TRUE_FALSE: INTERACTION_TRUE_FALSE,
        CHOICE: INTERACTION_CHOICE,
        FILL_IN: INTERACTION_FILL_IN,
        LONG_FILL_IN: INTERACTION_LONG_FILL_IN,
        MATCHING: INTERACTION_MATCHING,
        PERFORMANCE: INTERACTION_PERFORMANCE,
        SEQUENCING: INTERACTION_SEQUENCING,
        LIKERT: INTERACTION_LIKERT,
        NUMERIC: INTERACTION_NUMERIC,
        OTHER: INTERACTION_OTHER
    },
    RESULT: {
        CORRECT: RESULT_CORRECT,
        INCORRECT: RESULT_INCORRECT,
        UNANTICIPATED: RESULT_UNANTICIPATED,
        NEUTRAL: RESULT_NEUTRAL
    }
};
